# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
prisma==0.11.0
asyncpg==0.29.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
PyJWT==2.8.0

# Redis for caching and sessions
redis==5.0.1
aioredis==2.0.1

# AI and ML libraries
langchain
langchain-google-genai
langchain-openai
langgraph
pinecone-client==2.2.4

# Document processing
python-docx==1.1.0
PyPDF2==3.0.1
python-magic==0.4.27

# Cloud storage
cloudinary==1.36.0

# Streamlit frontend
streamlit==1.28.2
plotly==5.17.0
pandas==2.1.4

# Job scraping (existing jobspy)
# Note: jobspy is already in the project directory

# Configuration and environment
pydantic[email]==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Web automation
selenium==4.15.2
webdriver-manager==4.0.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0